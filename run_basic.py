#!/usr/bin/env python3
"""
Basic launcher for SMS Organizer that checks dependencies and provides helpful messages.
"""

import sys
import os

def check_dependencies():
    """Check for optional dependencies and provide helpful messages"""
    missing_deps = []
    
    # Check for PIL/Pillow
    try:
        from PIL import Image, ImageTk
        print("✓ Pillow found - Full image support available")
    except ImportError:
        missing_deps.append("Pillow")
        print("⚠ Pillow not found - Image viewing will be limited")
    
    # Check for ReportLab
    try:
        import reportlab
        print("✓ ReportLab found - PDF export available")
    except ImportError:
        missing_deps.append("reportlab")
        print("⚠ ReportLab not found - PDF export will be disabled")
    
    if missing_deps:
        print("\n" + "="*50)
        print("OPTIONAL DEPENDENCIES MISSING")
        print("="*50)
        print("The application will work without these, but some features will be limited:")
        print()
        for dep in missing_deps:
            if dep == "Pillow":
                print(f"• {dep}: Enables image thumbnails and full-size image viewing")
                print(f"  Install with: pip install Pillow")
            elif dep == "reportlab":
                print(f"• {dep}: Enables PDF export functionality")
                print(f"  Install with: pip install reportlab")
        
        print("\nTo install all optional dependencies:")
        print("pip install -r requirements.txt")
        print("\nOr install individually:")
        print("pip install Pillow reportlab")
        print("\n" + "="*50)
        
        response = input("Continue anyway? (y/n): ").lower().strip()
        if response not in ['y', 'yes']:
            print("Exiting. Install dependencies and try again.")
            return False
    
    return True

def main():
    """Main launcher function"""
    print("SMS Organizer - Phone SMS Backup Viewer")
    print("="*40)
    
    # Check if we're in the right directory
    if not os.path.exists("sms_organizer.py"):
        print("Error: sms_organizer.py not found in current directory")
        print("Please run this script from the SMS Organizer directory")
        return 1
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    print("\nStarting SMS Organizer...")
    print("Loading application...")
    
    try:
        # Import and run the main application
        from sms_organizer import SMSOrganizerApp
        app = SMSOrganizerApp()
        app.run()
        return 0
    except ImportError as e:
        print(f"\nError importing application: {e}")
        print("Please ensure all required files are present:")
        print("- sms_organizer.py")
        print("- sms_parser.py") 
        print("- theme_manager.py")
        print("- media_viewer.py")
        print("- export_manager.py")
        return 1
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        print("Please check the error message above and try again.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
