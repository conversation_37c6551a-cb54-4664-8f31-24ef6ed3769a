# SMS Organizer - Phone SMS Backup Viewer

A desktop application for viewing and organizing XML SMS backups from Android phones. This application allows you to easily browse, search, and organize your text messages and multimedia messages from XML backup files.

## Features

- **View SMS and MMS Messages**: Display both text messages and multimedia messages
- **Contact Organization**: Automatically group messages by contact/phone number
- **Conversation View**: See messages in a chat-like conversation format
- **Message List View**: Browse messages in a detailed list format
- **Search Functionality**: Search through message content
- **Contact Name Resolution**: Display contact names when available in backup
- **Statistics**: View backup file statistics
- **Cross-Platform**: Works on Windows, macOS, and Linux

## Requirements

- Python 3.7 or higher
- tkinter (usually included with Python)
- No additional dependencies required!

## Installation

1. Download or clone the project files
2. Ensure you have Python 3.7+ installed
3. No additional installation required - the application uses only Python standard library

## Usage

### Running the Application

```bash
python sms_organizer.py
```

### Loading an XML Backup File

1. Click "Open XML" button or use File → Open XML Backup...
2. Select your SMS backup XML file
3. The application will parse the file and display contacts in the left panel

### Viewing Messages

1. Select a contact from the left panel
2. View messages in either:
   - **Conversation tab**: Chat-like view with sent/received formatting
   - **Message List tab**: Detailed list view with timestamps and message types

### Searching Messages

1. Use the search box in the toolbar for quick searches
2. Or use Tools → Search Messages for advanced search options

### Keyboard Shortcuts

- `Ctrl+O`: Open XML backup file
- `Ctrl+F`: Open search dialog
- `Ctrl+Q`: Quit application
- `F5`: Refresh view

## Supported XML Formats

This application supports XML backup files created by popular Android SMS backup applications such as:

- SMS Backup & Restore
- SMS Backup+
- Other apps that follow the standard Android SMS backup XML format

### XML Structure

The application can parse:

- **SMS Messages**: Standard text messages with sender, timestamp, content
- **MMS Messages**: Multimedia messages with text and media attachments
- **Call Logs**: Phone call history (displayed in statistics)

## File Structure

```
SMS Organizer/
├── sms_organizer.py          # Main GUI application
├── sms_parser.py             # XML parsing module
├── test_sms_organizer.py     # Test suite
├── sample_sms_backup.xml     # Sample XML file for testing
└── README.md                 # This file
```

## Testing

Run the test suite to verify everything works correctly:

```bash
python test_sms_organizer.py
```

This will:
1. Run unit tests on the XML parser
2. Optionally launch the GUI for manual testing

## Sample Data

A sample XML backup file (`sample_sms_backup.xml`) is included for testing. It contains:
- Sample SMS conversations between multiple contacts
- Sample MMS messages with text content
- Proper XML structure following Android backup standards

## XML Field Reference

### SMS Messages
- `address`: Phone number of sender/recipient
- `date`: Java timestamp (milliseconds since epoch)
- `type`: 1=Received, 2=Sent, 3=Draft, 4=Outbox, 5=Failed, 6=Queued
- `body`: Message content
- `read`: 1=Read, 0=Unread
- `contact_name`: Contact name (if available)

### MMS Messages
- `date`: Java timestamp
- `msg_box`: 1=Received, 2=Sent, 3=Draft, 4=Outbox
- `parts`: Message parts (text, images, etc.)
- `addrs`: List of recipients/senders

## Troubleshooting

### Common Issues

1. **"Failed to parse XML file"**
   - Ensure the file is a valid XML backup from an Android SMS app
   - Check that the file isn't corrupted or truncated

2. **"No contacts found"**
   - Some backup files may not include contact names
   - Phone numbers will be displayed instead

3. **Application won't start**
   - Ensure Python 3.7+ is installed
   - Verify tkinter is available: `python -c "import tkinter"`

### Getting XML Backup Files

To create XML backup files from your Android phone:

1. Install "SMS Backup & Restore" from Google Play Store
2. Open the app and tap "Backup"
3. Choose backup location and format (XML)
4. Transfer the XML file to your computer

## Future Enhancements

Planned features for future versions:
- Export conversations to PDF, HTML, or text files
- Print conversation support
- Advanced search with date ranges and filters
- Message statistics and analytics
- Support for additional backup formats
- Dark mode theme

## Contributing

This is an open-source project. Feel free to:
- Report bugs or issues
- Suggest new features
- Submit pull requests
- Improve documentation

## License

This project is released under the MIT License. See the source code for full license details.

## Support

For support or questions:
1. Check the troubleshooting section above
2. Review the test suite for examples
3. Examine the sample XML file for format reference

---

**Note**: This application is designed to work with XML backup files from Android SMS backup applications. It does not directly access your phone or require any special permissions.
