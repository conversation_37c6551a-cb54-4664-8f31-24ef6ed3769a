"""
Export Manager for SMS Organizer

Handles exporting conversations to various formats (PDF, HTML, CSV, TXT).
"""

import csv
import html
import os
from datetime import datetime
from typing import List, Union, Dict, Optional
from tkinter import filedialog, messagebox
import webbrowser
import tempfile

# Try to import reportlab for PDF generation
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    print("ReportLab not available. PDF export will be disabled.")


class ExportManager:
    """Manages exporting conversations to different formats"""
    
    def __init__(self, parser, contacts):
        self.parser = parser
        self.contacts = contacts
    
    def export_conversation(self, phone_number: str, contact_name: str, format_type: str):
        """Export a conversation to specified format"""
        messages = self.parser.get_messages_by_contact(phone_number)
        
        if not messages:
            messagebox.showwarning("No Messages", "No messages found for this contact.")
            return
        
        # Get filename from user
        filename = self.get_export_filename(contact_name, format_type)
        if not filename:
            return
        
        try:
            if format_type == "pdf":
                self.export_to_pdf(messages, contact_name, phone_number, filename)
            elif format_type == "html":
                self.export_to_html(messages, contact_name, phone_number, filename)
            elif format_type == "csv":
                self.export_to_csv(messages, contact_name, phone_number, filename)
            elif format_type == "txt":
                self.export_to_txt(messages, contact_name, phone_number, filename)
            
            messagebox.showinfo("Export Complete", f"Conversation exported to {filename}")
            
            # Ask if user wants to open the file
            if messagebox.askyesno("Open File", "Would you like to open the exported file?"):
                self.open_file(filename)
                
        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to export conversation:\n{str(e)}")
    
    def get_export_filename(self, contact_name: str, format_type: str) -> Optional[str]:
        """Get filename for export"""
        # Clean contact name for filename
        safe_name = "".join(c for c in contact_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        default_name = f"{safe_name}_conversation.{format_type}"
        
        filetypes = {
            "pdf": [("PDF files", "*.pdf"), ("All files", "*.*")],
            "html": [("HTML files", "*.html"), ("All files", "*.*")],
            "csv": [("CSV files", "*.csv"), ("All files", "*.*")],
            "txt": [("Text files", "*.txt"), ("All files", "*.*")]
        }
        
        return filedialog.asksaveasfilename(
            title=f"Export Conversation as {format_type.upper()}",
            defaultextension=f".{format_type}",
            initialvalue=default_name,
            filetypes=filetypes.get(format_type, [("All files", "*.*")])
        )
    
    def export_to_pdf(self, messages: List, contact_name: str, phone_number: str, filename: str):
        """Export conversation to PDF"""
        if not PDF_AVAILABLE:
            raise Exception("PDF export requires reportlab library. Install with: pip install reportlab")
        
        doc = SimpleDocTemplate(filename, pagesize=letter)
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
            alignment=1  # Center
        )
        story.append(Paragraph(f"SMS Conversation with {contact_name}", title_style))
        story.append(Paragraph(f"Phone: {phone_number}", styles['Normal']))
        story.append(Paragraph(f"Exported: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Messages
        for message in messages:
            if hasattr(message, 'type'):  # SMS
                direction = "Sent" if message.type == 2 else "Received"
                content = message.body
                msg_type = "SMS"
            else:  # MMS
                direction = "Sent" if message.msg_box == 2 else "Received"
                content = ""
                for part in message.parts:
                    if part.text:
                        content += part.text + " "
                if not content.strip():
                    content = "[MMS with media content]"
                msg_type = "MMS"
            
            timestamp = message.get_readable_date()
            
            # Message header
            header_style = ParagraphStyle(
                'MessageHeader',
                parent=styles['Normal'],
                fontSize=10,
                textColor=colors.grey,
                spaceAfter=5
            )
            story.append(Paragraph(f"{direction} - {timestamp} ({msg_type})", header_style))
            
            # Message content
            content_style = ParagraphStyle(
                'MessageContent',
                parent=styles['Normal'],
                fontSize=11,
                leftIndent=20,
                spaceAfter=15,
                backColor=colors.lightgrey if direction == "Received" else colors.lightgreen
            )
            story.append(Paragraph(html.escape(content), content_style))
        
        doc.build(story)
    
    def export_to_html(self, messages: List, contact_name: str, phone_number: str, filename: str):
        """Export conversation to HTML"""
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>SMS Conversation with {html.escape(contact_name)}</title>
    <meta charset="UTF-8">
    <style>
        body {{
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .conversation {{
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .message {{
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
            max-width: 70%;
        }}
        .sent {{
            background-color: #DCF8C6;
            margin-left: auto;
            text-align: right;
        }}
        .received {{
            background-color: #FFFFFF;
            border: 1px solid #e0e0e0;
        }}
        .mms {{
            background-color: #E1F5FE;
        }}
        .timestamp {{
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }}
        .content {{
            font-size: 14px;
            line-height: 1.4;
        }}
        .message-type {{
            font-size: 10px;
            color: #999;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>SMS Conversation with {html.escape(contact_name)}</h1>
        <p><strong>Phone:</strong> {html.escape(phone_number)}</p>
        <p><strong>Exported:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p><strong>Total Messages:</strong> {len(messages)}</p>
    </div>
    
    <div class="conversation">
"""
        
        for message in messages:
            if hasattr(message, 'type'):  # SMS
                direction = "sent" if message.type == 2 else "received"
                content = html.escape(message.body)
                msg_type = "SMS"
            else:  # MMS
                direction = "sent" if message.msg_box == 2 else "received"
                content = ""
                for part in message.parts:
                    if part.text:
                        content += html.escape(part.text) + " "
                if not content.strip():
                    content = "<em>[MMS with media content]</em>"
                msg_type = "MMS"
                direction += " mms"
            
            timestamp = html.escape(message.get_readable_date())
            sender = "You" if "sent" in direction else contact_name
            
            html_content += f"""
        <div class="message {direction}">
            <div class="timestamp">{sender} - {timestamp} <span class="message-type">{msg_type}</span></div>
            <div class="content">{content}</div>
        </div>
"""
        
        html_content += """
    </div>
</body>
</html>
"""
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def export_to_csv(self, messages: List, contact_name: str, phone_number: str, filename: str):
        """Export conversation to CSV"""
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Header
            writer.writerow(['Contact', 'Phone', 'Date', 'Time', 'Direction', 'Type', 'Content'])
            
            for message in messages:
                if hasattr(message, 'type'):  # SMS
                    direction = "Sent" if message.type == 2 else "Received"
                    content = message.body
                    msg_type = "SMS"
                else:  # MMS
                    direction = "Sent" if message.msg_box == 2 else "Received"
                    content = ""
                    for part in message.parts:
                        if part.text:
                            content += part.text + " "
                    if not content.strip():
                        content = "[MMS with media content]"
                    msg_type = "MMS"
                
                # Parse timestamp
                try:
                    dt = datetime.fromtimestamp(message.date / 1000)
                    date_str = dt.strftime('%Y-%m-%d')
                    time_str = dt.strftime('%H:%M:%S')
                except:
                    date_str = "Unknown"
                    time_str = "Unknown"
                
                writer.writerow([
                    contact_name,
                    phone_number,
                    date_str,
                    time_str,
                    direction,
                    msg_type,
                    content
                ])
    
    def export_to_txt(self, messages: List, contact_name: str, phone_number: str, filename: str):
        """Export conversation to plain text"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"SMS Conversation with {contact_name}\n")
            f.write(f"Phone: {phone_number}\n")
            f.write(f"Exported: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total Messages: {len(messages)}\n")
            f.write("=" * 50 + "\n\n")
            
            for message in messages:
                if hasattr(message, 'type'):  # SMS
                    direction = "You" if message.type == 2 else contact_name
                    content = message.body
                    msg_type = "SMS"
                else:  # MMS
                    direction = "You" if message.msg_box == 2 else contact_name
                    content = ""
                    for part in message.parts:
                        if part.text:
                            content += part.text + " "
                    if not content.strip():
                        content = "[MMS with media content]"
                    msg_type = "MMS"
                
                timestamp = message.get_readable_date()
                
                f.write(f"{direction} - {timestamp} ({msg_type})\n")
                f.write(f"{content}\n\n")
    
    def export_all_conversations(self, format_type: str):
        """Export all conversations to a single file"""
        if not self.contacts:
            messagebox.showwarning("No Contacts", "No contacts found to export.")
            return
        
        filename = filedialog.asksaveasfilename(
            title=f"Export All Conversations as {format_type.upper()}",
            defaultextension=f".{format_type}",
            initialvalue=f"all_conversations.{format_type}",
            filetypes=[(f"{format_type.upper()} files", f"*.{format_type}"), ("All files", "*.*")]
        )
        
        if not filename:
            return
        
        try:
            if format_type == "csv":
                self.export_all_to_csv(filename)
            elif format_type == "txt":
                self.export_all_to_txt(filename)
            elif format_type == "html":
                self.export_all_to_html(filename)
            
            messagebox.showinfo("Export Complete", f"All conversations exported to {filename}")
            
            if messagebox.askyesno("Open File", "Would you like to open the exported file?"):
                self.open_file(filename)
                
        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to export conversations:\n{str(e)}")
    
    def export_all_to_csv(self, filename: str):
        """Export all conversations to CSV"""
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Contact', 'Phone', 'Date', 'Time', 'Direction', 'Type', 'Content'])
            
            for phone, contact_name in self.contacts.items():
                messages = self.parser.get_messages_by_contact(phone)
                for message in messages:
                    if hasattr(message, 'type'):  # SMS
                        direction = "Sent" if message.type == 2 else "Received"
                        content = message.body
                        msg_type = "SMS"
                    else:  # MMS
                        direction = "Sent" if message.msg_box == 2 else "Received"
                        content = ""
                        for part in message.parts:
                            if part.text:
                                content += part.text + " "
                        if not content.strip():
                            content = "[MMS with media content]"
                        msg_type = "MMS"
                    
                    try:
                        dt = datetime.fromtimestamp(message.date / 1000)
                        date_str = dt.strftime('%Y-%m-%d')
                        time_str = dt.strftime('%H:%M:%S')
                    except:
                        date_str = "Unknown"
                        time_str = "Unknown"
                    
                    writer.writerow([contact_name, phone, date_str, time_str, direction, msg_type, content])
    
    def export_all_to_txt(self, filename: str):
        """Export all conversations to text file"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("All SMS Conversations\n")
            f.write(f"Exported: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 50 + "\n\n")
            
            for phone, contact_name in self.contacts.items():
                messages = self.parser.get_messages_by_contact(phone)
                if messages:
                    f.write(f"Conversation with {contact_name} ({phone})\n")
                    f.write("-" * 40 + "\n")
                    
                    for message in messages:
                        if hasattr(message, 'type'):  # SMS
                            direction = "You" if message.type == 2 else contact_name
                            content = message.body
                            msg_type = "SMS"
                        else:  # MMS
                            direction = "You" if message.msg_box == 2 else contact_name
                            content = ""
                            for part in message.parts:
                                if part.text:
                                    content += part.text + " "
                            if not content.strip():
                                content = "[MMS with media content]"
                            msg_type = "MMS"
                        
                        timestamp = message.get_readable_date()
                        f.write(f"{direction} - {timestamp} ({msg_type})\n{content}\n\n")
                    
                    f.write("\n" + "=" * 50 + "\n\n")
    
    def export_all_to_html(self, filename: str):
        """Export all conversations to HTML"""
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>All SMS Conversations</title>
    <meta charset="UTF-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .conversation {{ margin-bottom: 40px; border: 1px solid #ccc; padding: 20px; }}
        .contact-header {{ background-color: #f0f0f0; padding: 10px; margin: -20px -20px 20px -20px; }}
        .message {{ margin-bottom: 10px; padding: 8px; border-radius: 5px; }}
        .sent {{ background-color: #DCF8C6; text-align: right; }}
        .received {{ background-color: #FFFFFF; border: 1px solid #e0e0e0; }}
        .timestamp {{ font-size: 12px; color: #666; }}
    </style>
</head>
<body>
    <h1>All SMS Conversations</h1>
    <p>Exported: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
"""
        
        for phone, contact_name in self.contacts.items():
            messages = self.parser.get_messages_by_contact(phone)
            if messages:
                html_content += f"""
    <div class="conversation">
        <div class="contact-header">
            <h2>{html.escape(contact_name)} ({html.escape(phone)})</h2>
        </div>
"""
                
                for message in messages:
                    if hasattr(message, 'type'):  # SMS
                        direction = "sent" if message.type == 2 else "received"
                        content = html.escape(message.body)
                        msg_type = "SMS"
                    else:  # MMS
                        direction = "sent" if message.msg_box == 2 else "received"
                        content = ""
                        for part in message.parts:
                            if part.text:
                                content += html.escape(part.text) + " "
                        if not content.strip():
                            content = "<em>[MMS with media content]</em>"
                        msg_type = "MMS"
                    
                    timestamp = html.escape(message.get_readable_date())
                    sender = "You" if direction == "sent" else contact_name
                    
                    html_content += f"""
        <div class="message {direction}">
            <div class="timestamp">{sender} - {timestamp} ({msg_type})</div>
            <div>{content}</div>
        </div>
"""
                
                html_content += "    </div>\n"
        
        html_content += """
</body>
</html>
"""
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def open_file(self, filename: str):
        """Open file with system default application"""
        try:
            import platform
            import subprocess
            
            system = platform.system()
            if system == "Windows":
                os.startfile(filename)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", filename])
            else:  # Linux
                subprocess.run(["xdg-open", filename])
        except Exception as e:
            print(f"Could not open file: {e}")
    
    def get_statistics(self) -> Dict:
        """Get conversation statistics"""
        stats = {
            "total_sms": len(self.parser.sms_messages),
            "total_mms": len(self.parser.mms_messages),
            "total_contacts": len(self.contacts),
            "sent_sms": len([msg for msg in self.parser.sms_messages if msg.type == 2]),
            "received_sms": len([msg for msg in self.parser.sms_messages if msg.type == 1]),
            "sent_mms": len([msg for msg in self.parser.mms_messages if msg.msg_box == 2]),
            "received_mms": len([msg for msg in self.parser.mms_messages if msg.msg_box == 1])
        }
        
        # Most active contact
        contact_counts = {}
        for phone, name in self.contacts.items():
            messages = self.parser.get_messages_by_contact(phone)
            contact_counts[name] = len(messages)
        
        if contact_counts:
            stats["most_active_contact"] = max(contact_counts, key=contact_counts.get)
            stats["most_active_count"] = contact_counts[stats["most_active_contact"]]
        else:
            stats["most_active_contact"] = "None"
            stats["most_active_count"] = 0
        
        return stats
