"""
Media Viewer for MMS attachments

Handles viewing images, videos, and other media files from MMS messages.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import base64
import io
import os
import tempfile
from typing import Optional, List
import subprocess
import platform

# Try to import PIL, provide fallback if not available
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("PIL (Pillow) not available. Image viewing will be limited.")
    print("To enable full image support, install Pillow: pip install Pillow")

    # Create dummy classes for fallback
    class Image:
        @staticmethod
        def open(*args, **kwargs):
            raise ImportError("PIL not available")

        class Resampling:
            LANCZOS = None

    class ImageTk:
        @staticmethod
        def PhotoImage(*args, **kwargs):
            raise ImportError("PIL not available")


class MediaViewer:
    """Handles viewing media attachments from MMS messages"""
    
    def __init__(self, parent, theme_manager=None):
        self.parent = parent
        self.theme_manager = theme_manager
        self.temp_files = []  # Keep track of temporary files
    
    def __del__(self):
        """Clean up temporary files"""
        self.cleanup_temp_files()
    
    def cleanup_temp_files(self):
        """Remove temporary files"""
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except Exception:
                pass
        self.temp_files.clear()
    
    def decode_base64_data(self, base64_data: str) -> Optional[bytes]:
        """Decode base64 data to bytes"""
        try:
            return base64.b64decode(base64_data)
        except Exception as e:
            print(f"Error decoding base64 data: {e}")
            return None
    
    def get_media_type(self, content_type: str) -> str:
        """Determine media type from content type"""
        if not content_type:
            return "unknown"
        
        content_type = content_type.lower()
        if content_type.startswith("image/"):
            return "image"
        elif content_type.startswith("video/"):
            return "video"
        elif content_type.startswith("audio/"):
            return "audio"
        elif content_type.startswith("text/"):
            return "text"
        else:
            return "unknown"
    
    def create_thumbnail(self, image_data: bytes, size: tuple = (100, 100)) -> Optional[ImageTk.PhotoImage]:
        """Create a thumbnail from image data"""
        if not PIL_AVAILABLE:
            return None

        try:
            image = Image.open(io.BytesIO(image_data))
            image.thumbnail(size, Image.Resampling.LANCZOS)
            return ImageTk.PhotoImage(image)
        except Exception as e:
            print(f"Error creating thumbnail: {e}")
            return None
    
    def show_image_viewer(self, image_data: bytes, title: str = "Image Viewer"):
        """Show image in a popup window"""
        if not PIL_AVAILABLE:
            messagebox.showwarning("PIL Required",
                                 "Image viewing requires Pillow library.\n"
                                 "Install with: pip install Pillow\n\n"
                                 "Opening with system viewer instead...")
            self.open_with_system_viewer(image_data, "image/jpeg")
            return

        try:
            # Create new window
            viewer_window = tk.Toplevel(self.parent)
            viewer_window.title(title)
            viewer_window.geometry("800x600")
            
            # Apply theme if available
            if self.theme_manager:
                theme = self.theme_manager.get_current_theme()
                viewer_window.configure(bg=theme["bg"])
            
            # Create scrollable frame
            canvas = tk.Canvas(viewer_window)
            scrollbar_v = ttk.Scrollbar(viewer_window, orient="vertical", command=canvas.yview)
            scrollbar_h = ttk.Scrollbar(viewer_window, orient="horizontal", command=canvas.xview)
            scrollable_frame = ttk.Frame(canvas)
            
            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )
            
            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
            
            # Load and display image
            image = Image.open(io.BytesIO(image_data))
            photo = ImageTk.PhotoImage(image)
            
            image_label = tk.Label(scrollable_frame, image=photo)
            image_label.image = photo  # Keep a reference
            image_label.pack(padx=10, pady=10)
            
            # Add image info
            info_text = f"Size: {image.size[0]} x {image.size[1]} pixels\n"
            info_text += f"Mode: {image.mode}\n"
            info_text += f"Format: {image.format}"
            
            info_label = tk.Label(scrollable_frame, text=info_text, justify=tk.LEFT)
            if self.theme_manager:
                self.theme_manager.apply_theme_to_widget(info_label, "label")
            info_label.pack(pady=5)
            
            # Pack scrollbars and canvas
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar_v.pack(side="right", fill="y")
            scrollbar_h.pack(side="bottom", fill="x")
            
            # Add save button
            button_frame = ttk.Frame(viewer_window)
            button_frame.pack(side="bottom", fill="x", padx=5, pady=5)
            
            save_button = ttk.Button(button_frame, text="Save Image", 
                                   command=lambda: self.save_media(image_data, "image"))
            save_button.pack(side="right", padx=5)
            
        except Exception as e:
            messagebox.showerror("Error", f"Could not display image: {e}")
    
    def save_media(self, media_data: bytes, media_type: str):
        """Save media data to file"""
        from tkinter import filedialog
        
        # Determine file extension
        extensions = {
            "image": [("JPEG files", "*.jpg"), ("PNG files", "*.png"), ("All files", "*.*")],
            "video": [("MP4 files", "*.mp4"), ("AVI files", "*.avi"), ("All files", "*.*")],
            "audio": [("MP3 files", "*.mp3"), ("WAV files", "*.wav"), ("All files", "*.*")],
            "text": [("Text files", "*.txt"), ("All files", "*.*")]
        }
        
        filetypes = extensions.get(media_type, [("All files", "*.*")])
        
        filename = filedialog.asksaveasfilename(
            title=f"Save {media_type.title()}",
            filetypes=filetypes
        )
        
        if filename:
            try:
                with open(filename, 'wb') as f:
                    f.write(media_data)
                messagebox.showinfo("Success", f"{media_type.title()} saved successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Could not save {media_type}: {e}")
    
    def open_with_system_viewer(self, media_data: bytes, content_type: str):
        """Open media with system default application"""
        try:
            # Create temporary file
            suffix = self.get_file_extension(content_type)
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
            temp_file.write(media_data)
            temp_file.close()
            
            self.temp_files.append(temp_file.name)
            
            # Open with system default application
            system = platform.system()
            if system == "Windows":
                os.startfile(temp_file.name)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", temp_file.name])
            else:  # Linux
                subprocess.run(["xdg-open", temp_file.name])
                
        except Exception as e:
            messagebox.showerror("Error", f"Could not open media: {e}")
    
    def get_file_extension(self, content_type: str) -> str:
        """Get file extension from content type"""
        extensions = {
            "image/jpeg": ".jpg",
            "image/png": ".png",
            "image/gif": ".gif",
            "image/bmp": ".bmp",
            "video/mp4": ".mp4",
            "video/avi": ".avi",
            "video/quicktime": ".mov",
            "audio/mpeg": ".mp3",
            "audio/wav": ".wav",
            "text/plain": ".txt",
            "text/html": ".html"
        }
        return extensions.get(content_type.lower(), ".bin")
    
    def create_media_widget(self, parent_frame, mms_part, max_thumbnail_size=(80, 80)):
        """Create a widget to display media content"""
        media_frame = ttk.Frame(parent_frame)
        
        content_type = mms_part.ct or ""
        media_type = self.get_media_type(content_type)
        
        if mms_part.data:
            # Decode base64 data
            media_data = self.decode_base64_data(mms_part.data)
            if not media_data:
                # Show error message
                error_label = tk.Label(media_frame, text="[Invalid media data]", 
                                     fg="red", font=("Arial", 8))
                error_label.pack()
                return media_frame
            
            if media_type == "image":
                # Create thumbnail
                thumbnail = self.create_thumbnail(media_data, max_thumbnail_size)
                if thumbnail:
                    # Create clickable thumbnail
                    thumbnail_label = tk.Label(media_frame, image=thumbnail, cursor="hand2")
                    thumbnail_label.image = thumbnail  # Keep reference
                    thumbnail_label.pack()

                    # Bind click event to open full image
                    thumbnail_label.bind("<Button-1>",
                                       lambda e: self.show_image_viewer(media_data,
                                                                       f"Image - {mms_part.name or 'Untitled'}"))

                    # Add context menu
                    self.add_media_context_menu(thumbnail_label, media_data, content_type)
                else:
                    # Show placeholder (either PIL not available or thumbnail creation failed)
                    placeholder_text = "[Image]"
                    if not PIL_AVAILABLE:
                        placeholder_text = "[Image - Click to view]"

                    placeholder_label = tk.Label(media_frame, text=placeholder_text,
                                                fg="blue", cursor="hand2")
                    placeholder_label.pack()
                    placeholder_label.bind("<Button-1>",
                                         lambda e: self.show_image_viewer(media_data,
                                                                         f"Image - {mms_part.name or 'Untitled'}"))

                    # Add context menu
                    self.add_media_context_menu(placeholder_label, media_data, content_type)
            
            elif media_type in ["video", "audio"]:
                # Show media icon with click to open
                media_text = f"[{media_type.title()}]"
                if mms_part.name:
                    media_text += f"\n{mms_part.name}"
                
                media_label = tk.Label(media_frame, text=media_text, 
                                     fg="blue", cursor="hand2", justify=tk.CENTER)
                media_label.pack()
                media_label.bind("<Button-1>", 
                               lambda e: self.open_with_system_viewer(media_data, content_type))
                
                # Add context menu
                self.add_media_context_menu(media_label, media_data, content_type)
            
            else:
                # Unknown media type
                unknown_text = f"[{content_type or 'Unknown media'}]"
                if mms_part.name:
                    unknown_text += f"\n{mms_part.name}"
                
                unknown_label = tk.Label(media_frame, text=unknown_text, 
                                       fg="gray", justify=tk.CENTER)
                unknown_label.pack()
        
        else:
            # No data available
            no_data_label = tk.Label(media_frame, text="[No media data]", 
                                   fg="gray", font=("Arial", 8))
            no_data_label.pack()
        
        return media_frame
    
    def add_media_context_menu(self, widget, media_data: bytes, content_type: str):
        """Add right-click context menu to media widget"""
        context_menu = tk.Menu(widget, tearoff=0)
        
        media_type = self.get_media_type(content_type)
        
        if media_type == "image":
            context_menu.add_command(label="View Full Size", 
                                   command=lambda: self.show_image_viewer(media_data))
        
        context_menu.add_command(label="Open with System Viewer", 
                               command=lambda: self.open_with_system_viewer(media_data, content_type))
        context_menu.add_command(label="Save As...", 
                               command=lambda: self.save_media(media_data, media_type))
        
        def show_context_menu(event):
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
        
        widget.bind("<Button-3>", show_context_menu)  # Right-click
        if platform.system() == "Darwin":  # macOS
            widget.bind("<Button-2>", show_context_menu)  # Control-click



