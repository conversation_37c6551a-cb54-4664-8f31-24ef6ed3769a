"""
SMS XML Backup Parser Module

This module provides functionality to parse XML backup files from Android SMS backup apps.
Supports SMS messages, MMS messages, and call logs.
"""

import xml.etree.ElementTree as ET
from datetime import datetime
import base64
from typing import List, Dict, Optional, Union
from dataclasses import dataclass


@dataclass
class SMSMessage:
    """Represents an SMS message"""
    protocol: int
    address: str
    date: int
    type: int  # 1=Received, 2=Sent, 3=Draft, 4=Outbox, 5=Failed, 6=Queued
    subject: Optional[str]
    body: str
    toa: Optional[str]
    sc_toa: Optional[str]
    service_center: Optional[str]
    read: int  # 1=Read, 0=Unread
    status: int  # -1=None, 0=Complete, 32=Pending, 64=Failed
    sub_id: Optional[int]
    readable_date: Optional[str]
    contact_name: Optional[str]
    
    def get_readable_date(self) -> str:
        """Convert Java timestamp to readable date"""
        if self.readable_date:
            return self.readable_date
        try:
            # Convert from Java timestamp (milliseconds) to Python timestamp (seconds)
            timestamp = self.date / 1000
            return datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, OSError):
            return "Unknown Date"
    
    def get_type_string(self) -> str:
        """Get human-readable message type"""
        type_map = {
            1: "Received",
            2: "Sent", 
            3: "Draft",
            4: "Outbox",
            5: "Failed",
            6: "Queued"
        }
        return type_map.get(self.type, "Unknown")


@dataclass
class MMSPart:
    """Represents a part of an MMS message"""
    seq: Optional[int]
    ct: Optional[str]  # Content type
    name: Optional[str]
    chset: Optional[str]  # Charset
    cl: Optional[str]  # Content location
    text: Optional[str]
    data: Optional[str]  # Base64 encoded binary data


@dataclass
class MMSAddress:
    """Represents an address in an MMS message"""
    address: str
    type: int  # 129=BCC, 130=CC, 151=To, 137=From
    charset: Optional[str]


@dataclass
class MMSMessage:
    """Represents an MMS message"""
    date: int
    ct_t: Optional[str]  # Content type
    msg_box: int  # 1=Received, 2=Sent, 3=Draft, 4=Outbox
    rr: Optional[str]  # Read report
    sub: Optional[str]  # Subject
    read_status: Optional[int]
    address: Optional[str]
    m_id: Optional[str]  # Message ID
    read: Optional[int]
    m_size: Optional[int]
    m_type: Optional[int]
    sim_slot: Optional[int]
    readable_date: Optional[str]
    contact_name: Optional[str]
    parts: List[MMSPart]
    addresses: List[MMSAddress]
    
    def get_readable_date(self) -> str:
        """Convert Java timestamp to readable date"""
        if self.readable_date:
            return self.readable_date
        try:
            timestamp = self.date / 1000
            return datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, OSError):
            return "Unknown Date"


@dataclass
class CallLog:
    """Represents a call log entry"""
    number: str
    duration: int  # Duration in seconds
    date: int
    type: int  # 1=Incoming, 2=Outgoing, 3=Missed, 4=Voicemail, 5=Rejected, 6=Refused
    presentation: Optional[int]  # 1=Allowed, 2=Restricted, 3=Unknown, 4=Payphone
    subscription_id: Optional[str]
    readable_date: Optional[str]
    contact_name: Optional[str]
    
    def get_readable_date(self) -> str:
        """Convert Java timestamp to readable date"""
        if self.readable_date:
            return self.readable_date
        try:
            timestamp = self.date / 1000
            return datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, OSError):
            return "Unknown Date"
    
    def get_type_string(self) -> str:
        """Get human-readable call type"""
        type_map = {
            1: "Incoming",
            2: "Outgoing",
            3: "Missed",
            4: "Voicemail",
            5: "Rejected",
            6: "Refused"
        }
        return type_map.get(self.type, "Unknown")
    
    def get_duration_string(self) -> str:
        """Get formatted duration string"""
        if self.duration == 0:
            return "0s"
        
        hours = self.duration // 3600
        minutes = (self.duration % 3600) // 60
        seconds = self.duration % 60
        
        if hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"


class SMSXMLParser:
    """Parser for SMS XML backup files"""
    
    def __init__(self):
        self.sms_messages: List[SMSMessage] = []
        self.mms_messages: List[MMSMessage] = []
        self.call_logs: List[CallLog] = []
    
    def parse_file(self, file_path: str) -> bool:
        """
        Parse an XML backup file
        
        Args:
            file_path: Path to the XML backup file
            
        Returns:
            True if parsing was successful, False otherwise
        """
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            # Clear previous data
            self.sms_messages.clear()
            self.mms_messages.clear()
            self.call_logs.clear()
            
            # Parse different types of elements
            for element in root:
                if element.tag == 'sms':
                    self._parse_sms(element)
                elif element.tag == 'mms':
                    self._parse_mms(element)
                elif element.tag == 'call':
                    self._parse_call(element)
            
            return True
            
        except ET.ParseError as e:
            print(f"XML parsing error: {e}")
            return False
        except FileNotFoundError:
            print(f"File not found: {file_path}")
            return False
        except Exception as e:
            print(f"Unexpected error: {e}")
            return False
    
    def _parse_sms(self, element: ET.Element) -> None:
        """Parse an SMS element"""
        try:
            # Helper function to safely convert to int
            def safe_int(value, default=0):
                if value is None or value == 'null':
                    return default
                try:
                    return int(value)
                except (ValueError, TypeError):
                    return default

            # Helper function to handle null strings
            def safe_str(value, default=None):
                if value == 'null':
                    return default
                return value

            sms = SMSMessage(
                protocol=safe_int(element.get('protocol'), 0),
                address=element.get('address', ''),
                date=safe_int(element.get('date'), 0),
                type=safe_int(element.get('type'), 0),
                subject=safe_str(element.get('subject')),
                body=element.get('body', ''),
                toa=safe_str(element.get('toa')),
                sc_toa=safe_str(element.get('sc_toa')),
                service_center=safe_str(element.get('service_center')),
                read=safe_int(element.get('read'), 0),
                status=safe_int(element.get('status'), -1),
                sub_id=safe_int(element.get('sub_id')) if element.get('sub_id') and element.get('sub_id') != 'null' else None,
                readable_date=safe_str(element.get('readable_date')),
                contact_name=safe_str(element.get('contact_name'))
            )
            self.sms_messages.append(sms)
        except (ValueError, TypeError) as e:
            print(f"Error parsing SMS element: {e}")
    
    def _parse_mms(self, element: ET.Element) -> None:
        """Parse an MMS element"""
        try:
            # Parse MMS parts
            parts = []
            for part_elem in element.findall('parts/part'):
                seq_val = part_elem.get('seq')
                part = MMSPart(
                    seq=int(seq_val) if seq_val and seq_val != 'null' else None,
                    ct=part_elem.get('ct') if part_elem.get('ct') != 'null' else None,
                    name=part_elem.get('name') if part_elem.get('name') != 'null' else None,
                    chset=part_elem.get('chset') if part_elem.get('chset') != 'null' else None,
                    cl=part_elem.get('cl') if part_elem.get('cl') != 'null' else None,
                    text=part_elem.get('text') if part_elem.get('text') != 'null' else None,
                    data=part_elem.get('data') if part_elem.get('data') != 'null' else None
                )
                parts.append(part)

            # Parse MMS addresses
            addresses = []
            for addr_elem in element.findall('addrs/addr'):
                type_val = addr_elem.get('type', '0')
                addr = MMSAddress(
                    address=addr_elem.get('address', ''),
                    type=int(type_val) if type_val != 'null' else 0,
                    charset=addr_elem.get('charset') if addr_elem.get('charset') != 'null' else None
                )
                addresses.append(addr)

            # Helper function to safely convert to int
            def safe_int(value, default=None):
                if value is None or value == 'null':
                    return default
                try:
                    return int(value)
                except (ValueError, TypeError):
                    return default

            mms = MMSMessage(
                date=safe_int(element.get('date'), 0),
                ct_t=element.get('ct_t') if element.get('ct_t') != 'null' else None,
                msg_box=safe_int(element.get('msg_box'), 0),
                rr=element.get('rr') if element.get('rr') != 'null' else None,
                sub=element.get('sub') if element.get('sub') != 'null' else None,
                read_status=safe_int(element.get('read_status')),
                address=element.get('address') if element.get('address') != 'null' else None,
                m_id=element.get('m_id') if element.get('m_id') != 'null' else None,
                read=safe_int(element.get('read')),
                m_size=safe_int(element.get('m_size')),
                m_type=safe_int(element.get('m_type')),
                sim_slot=safe_int(element.get('sim_slot')),
                readable_date=element.get('readable_date') if element.get('readable_date') != 'null' else None,
                contact_name=element.get('contact_name') if element.get('contact_name') != 'null' else None,
                parts=parts,
                addresses=addresses
            )
            self.mms_messages.append(mms)
        except (ValueError, TypeError) as e:
            print(f"Error parsing MMS element: {e}")
    
    def _parse_call(self, element: ET.Element) -> None:
        """Parse a call log element"""
        try:
            call = CallLog(
                number=element.get('number', ''),
                duration=int(element.get('duration', 0)),
                date=int(element.get('date', 0)),
                type=int(element.get('type', 0)),
                presentation=int(element.get('presentation')) if element.get('presentation') else None,
                subscription_id=element.get('subscription_id'),
                readable_date=element.get('readable_date'),
                contact_name=element.get('contact_name')
            )
            self.call_logs.append(call)
        except (ValueError, TypeError) as e:
            print(f"Error parsing call element: {e}")
    
    def get_contacts(self) -> Dict[str, str]:
        """
        Get a dictionary of phone numbers to contact names
        
        Returns:
            Dictionary mapping phone numbers to contact names
        """
        contacts = {}
        
        # Extract from SMS messages
        for sms in self.sms_messages:
            if sms.contact_name and sms.address:
                contacts[sms.address] = sms.contact_name
        
        # Extract from MMS messages
        for mms in self.mms_messages:
            if mms.contact_name and mms.address:
                contacts[mms.address] = mms.contact_name
        
        # Extract from call logs
        for call in self.call_logs:
            if call.contact_name and call.number:
                contacts[call.number] = call.contact_name
        
        return contacts
    
    def get_messages_by_contact(self, phone_number: str) -> List[Union[SMSMessage, MMSMessage]]:
        """
        Get all messages (SMS and MMS) for a specific phone number
        
        Args:
            phone_number: The phone number to filter by
            
        Returns:
            List of messages sorted by date
        """
        messages = []
        
        # Add SMS messages
        for sms in self.sms_messages:
            if sms.address == phone_number:
                messages.append(sms)
        
        # Add MMS messages
        for mms in self.mms_messages:
            if mms.address == phone_number:
                messages.append(mms)
        
        # Sort by date
        messages.sort(key=lambda x: x.date)
        
        return messages
    
    def search_messages(self, query: str, case_sensitive: bool = False) -> List[Union[SMSMessage, MMSMessage]]:
        """
        Search for messages containing the specified text
        
        Args:
            query: Text to search for
            case_sensitive: Whether the search should be case sensitive
            
        Returns:
            List of matching messages
        """
        if not case_sensitive:
            query = query.lower()
        
        results = []
        
        # Search SMS messages
        for sms in self.sms_messages:
            body = sms.body if case_sensitive else sms.body.lower()
            if query in body:
                results.append(sms)
        
        # Search MMS messages (text parts)
        for mms in self.mms_messages:
            for part in mms.parts:
                if part.text:
                    text = part.text if case_sensitive else part.text.lower()
                    if query in text:
                        results.append(mms)
                        break
        
        return results
