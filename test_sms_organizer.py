"""
Test script for SMS Organizer application
"""

import unittest
import os
from sms_parser import SMSXMLParser, SMSMessage, MMSMessage


class TestSMSParser(unittest.TestCase):
    """Test cases for SMS XML Parser"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.parser = SMSXMLParser()
        self.sample_file = "sample_sms_backup.xml"
    
    def test_parse_sample_file(self):
        """Test parsing the sample XML file"""
        if os.path.exists(self.sample_file):
            result = self.parser.parse_file(self.sample_file)
            self.assertTrue(result, "Failed to parse sample XML file")
            
            # Check that we have some messages
            self.assertGreater(len(self.parser.sms_messages), 0, "No SMS messages found")
            self.assertGreater(len(self.parser.mms_messages), 0, "No MMS messages found")
            
            print(f"Parsed {len(self.parser.sms_messages)} SMS messages")
            print(f"Parsed {len(self.parser.mms_messages)} MMS messages")
            print(f"Parsed {len(self.parser.call_logs)} call logs")
    
    def test_sms_message_properties(self):
        """Test SMS message properties"""
        if os.path.exists(self.sample_file):
            self.parser.parse_file(self.sample_file)
            
            if self.parser.sms_messages:
                sms = self.parser.sms_messages[0]
                
                # Test basic properties
                self.assertIsInstance(sms.address, str)
                self.assertIsInstance(sms.body, str)
                self.assertIsInstance(sms.date, int)
                self.assertIsInstance(sms.type, int)
                
                # Test readable date conversion
                readable_date = sms.get_readable_date()
                self.assertIsInstance(readable_date, str)
                self.assertNotEqual(readable_date, "Unknown Date")
                
                # Test type string conversion
                type_string = sms.get_type_string()
                self.assertIn(type_string, ["Received", "Sent", "Draft", "Outbox", "Failed", "Queued"])
                
                print(f"Sample SMS: {sms.address} - {sms.body[:50]}...")
    
    def test_mms_message_properties(self):
        """Test MMS message properties"""
        if os.path.exists(self.sample_file):
            self.parser.parse_file(self.sample_file)
            
            if self.parser.mms_messages:
                mms = self.parser.mms_messages[0]
                
                # Test basic properties
                self.assertIsInstance(mms.date, int)
                self.assertIsInstance(mms.msg_box, int)
                self.assertIsInstance(mms.parts, list)
                self.assertIsInstance(mms.addresses, list)
                
                # Test readable date conversion
                readable_date = mms.get_readable_date()
                self.assertIsInstance(readable_date, str)
                
                print(f"Sample MMS: {len(mms.parts)} parts, {len(mms.addresses)} addresses")
    
    def test_contacts_extraction(self):
        """Test contact extraction"""
        if os.path.exists(self.sample_file):
            self.parser.parse_file(self.sample_file)
            
            contacts = self.parser.get_contacts()
            self.assertIsInstance(contacts, dict)
            self.assertGreater(len(contacts), 0, "No contacts found")
            
            print(f"Found {len(contacts)} contacts:")
            for phone, name in contacts.items():
                print(f"  {phone}: {name}")
    
    def test_messages_by_contact(self):
        """Test getting messages by contact"""
        if os.path.exists(self.sample_file):
            self.parser.parse_file(self.sample_file)
            
            contacts = self.parser.get_contacts()
            if contacts:
                # Test with first contact
                phone_number = list(contacts.keys())[0]
                messages = self.parser.get_messages_by_contact(phone_number)
                
                self.assertIsInstance(messages, list)
                self.assertGreater(len(messages), 0, f"No messages found for {phone_number}")
                
                print(f"Found {len(messages)} messages for {phone_number}")
    
    def test_search_messages(self):
        """Test message search functionality"""
        if os.path.exists(self.sample_file):
            self.parser.parse_file(self.sample_file)
            
            # Search for a common word
            results = self.parser.search_messages("the")
            self.assertIsInstance(results, list)
            
            # Search for specific text that should exist
            results = self.parser.search_messages("dinner")
            if results:
                print(f"Found {len(results)} messages containing 'dinner'")
            
            # Test case-insensitive search
            results_lower = self.parser.search_messages("DINNER", case_sensitive=False)
            results_sensitive = self.parser.search_messages("DINNER", case_sensitive=True)
            
            # Case-insensitive should find more or equal results
            self.assertGreaterEqual(len(results_lower), len(results_sensitive))


def run_manual_test():
    """Run a manual test of the GUI application"""
    print("Starting manual GUI test...")
    print("This will open the SMS Organizer application.")
    print("You can test it by opening the sample_sms_backup.xml file.")
    
    try:
        from sms_organizer import SMSOrganizerApp
        app = SMSOrganizerApp()
        
        # Automatically load the sample file if it exists
        if os.path.exists("sample_sms_backup.xml"):
            app.load_file("sample_sms_backup.xml")
        
        app.run()
    except ImportError as e:
        print(f"Could not import GUI application: {e}")
    except Exception as e:
        print(f"Error running GUI application: {e}")


if __name__ == "__main__":
    print("SMS Organizer Test Suite")
    print("=" * 40)
    
    # Run unit tests
    print("\nRunning unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Ask user if they want to run manual GUI test
    print("\n" + "=" * 40)
    response = input("Do you want to run the manual GUI test? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        run_manual_test()
    else:
        print("Skipping GUI test.")
    
    print("\nTest completed!")
