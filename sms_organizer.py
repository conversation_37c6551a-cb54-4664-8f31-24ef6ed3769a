"""
SMS Organizer - Main GUI Application

A desktop application for viewing and organizing XML SMS backups from Android phones.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
from typing import Dict, List, Optional, Union
from sms_parser import SMSXMLParser, SMSMessage, MMSMessage, CallLog


class SMSOrganizerApp:
    """Main application class for SMS Organizer"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("SMS Organizer - Phone SMS Backup Viewer")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Initialize parser
        self.parser = SMSXMLParser()
        self.current_file = None
        self.contacts = {}
        
        # Create GUI components
        self.create_menu()
        self.create_toolbar()
        self.create_main_layout()
        self.create_status_bar()
        
        # Configure styles
        self.configure_styles()
        
        # Bind events
        self.bind_events()
    
    def create_menu(self):
        """Create the application menu bar"""
        self.menubar = tk.Menu(self.root)
        self.root.config(menu=self.menubar)
        
        # File menu
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open XML Backup...", command=self.open_file, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="Export Conversation...", command=self.export_conversation, state="disabled")
        file_menu.add_command(label="Print Conversation...", command=self.print_conversation, state="disabled")
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit, accelerator="Ctrl+Q")
        
        # View menu
        view_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Refresh", command=self.refresh_view, accelerator="F5")
        view_menu.add_separator()
        view_menu.add_command(label="Show SMS Only", command=lambda: self.filter_messages("sms"))
        view_menu.add_command(label="Show MMS Only", command=lambda: self.filter_messages("mms"))
        view_menu.add_command(label="Show All Messages", command=lambda: self.filter_messages("all"))
        
        # Tools menu
        tools_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Search Messages...", command=self.open_search, accelerator="Ctrl+F")
        tools_menu.add_command(label="Statistics", command=self.show_statistics)
        
        # Help menu
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def create_toolbar(self):
        """Create the toolbar"""
        self.toolbar = ttk.Frame(self.root)
        self.toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        # Open file button
        ttk.Button(self.toolbar, text="Open XML", command=self.open_file).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Search entry
        ttk.Label(self.toolbar, text="Search:").pack(side=tk.LEFT, padx=2)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(self.toolbar, textvariable=self.search_var, width=20)
        self.search_entry.pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="Search", command=self.quick_search).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Filter options
        ttk.Label(self.toolbar, text="Filter:").pack(side=tk.LEFT, padx=2)
        self.filter_var = tk.StringVar(value="all")
        filter_combo = ttk.Combobox(self.toolbar, textvariable=self.filter_var, 
                                   values=["all", "sms", "mms"], width=10, state="readonly")
        filter_combo.pack(side=tk.LEFT, padx=2)
        filter_combo.bind("<<ComboboxSelected>>", self.on_filter_change)
    
    def create_main_layout(self):
        """Create the main application layout"""
        # Create main paned window
        self.main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Contacts list
        self.left_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.left_frame, weight=1)
        
        # Contacts list
        ttk.Label(self.left_frame, text="Contacts", font=("Arial", 12, "bold")).pack(pady=5)
        
        contacts_frame = ttk.Frame(self.left_frame)
        contacts_frame.pack(fill=tk.BOTH, expand=True)
        
        # Contacts treeview
        self.contacts_tree = ttk.Treeview(contacts_frame, columns=("phone", "count"), show="tree headings")
        self.contacts_tree.heading("#0", text="Contact")
        self.contacts_tree.heading("phone", text="Phone Number")
        self.contacts_tree.heading("count", text="Messages")
        
        self.contacts_tree.column("#0", width=150)
        self.contacts_tree.column("phone", width=120)
        self.contacts_tree.column("count", width=80)
        
        # Scrollbars for contacts tree
        contacts_scrollbar_v = ttk.Scrollbar(contacts_frame, orient=tk.VERTICAL, command=self.contacts_tree.yview)
        contacts_scrollbar_h = ttk.Scrollbar(contacts_frame, orient=tk.HORIZONTAL, command=self.contacts_tree.xview)
        self.contacts_tree.configure(yscrollcommand=contacts_scrollbar_v.set, xscrollcommand=contacts_scrollbar_h.set)
        
        self.contacts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        contacts_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        contacts_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Right panel - Message view
        self.right_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.right_frame, weight=2)
        
        # Message view header
        self.message_header = ttk.Label(self.right_frame, text="Select a contact to view messages", 
                                       font=("Arial", 12, "bold"))
        self.message_header.pack(pady=5)
        
        # Messages notebook for different views
        self.messages_notebook = ttk.Notebook(self.right_frame)
        self.messages_notebook.pack(fill=tk.BOTH, expand=True)
        
        # Conversation view tab
        self.conversation_frame = ttk.Frame(self.messages_notebook)
        self.messages_notebook.add(self.conversation_frame, text="Conversation")
        
        # Create conversation view
        self.create_conversation_view()
        
        # Message list view tab
        self.list_frame = ttk.Frame(self.messages_notebook)
        self.messages_notebook.add(self.list_frame, text="Message List")
        
        # Create message list view
        self.create_message_list_view()
    
    def create_conversation_view(self):
        """Create the conversation-style message view"""
        # Conversation display
        self.conversation_text = scrolledtext.ScrolledText(
            self.conversation_frame, 
            wrap=tk.WORD, 
            state=tk.DISABLED,
            font=("Arial", 10)
        )
        self.conversation_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Configure text tags for different message types
        self.conversation_text.tag_configure("sent", background="#DCF8C6", justify="right", 
                                           rmargin=50, lmargin2=50)
        self.conversation_text.tag_configure("received", background="#FFFFFF", justify="left",
                                           lmargin1=50, lmargin2=50)
        self.conversation_text.tag_configure("timestamp", foreground="#666666", font=("Arial", 8))
        self.conversation_text.tag_configure("mms", background="#E1F5FE")
    
    def create_message_list_view(self):
        """Create the message list view"""
        # Message list treeview
        self.message_tree = ttk.Treeview(self.list_frame, 
                                        columns=("type", "date", "content"), 
                                        show="tree headings")
        self.message_tree.heading("#0", text="Direction")
        self.message_tree.heading("type", text="Type")
        self.message_tree.heading("date", text="Date/Time")
        self.message_tree.heading("content", text="Content")
        
        self.message_tree.column("#0", width=80)
        self.message_tree.column("type", width=60)
        self.message_tree.column("date", width=150)
        self.message_tree.column("content", width=300)
        
        # Scrollbars for message tree
        message_scrollbar_v = ttk.Scrollbar(self.list_frame, orient=tk.VERTICAL, command=self.message_tree.yview)
        message_scrollbar_h = ttk.Scrollbar(self.list_frame, orient=tk.HORIZONTAL, command=self.message_tree.xview)
        self.message_tree.configure(yscrollcommand=message_scrollbar_v.set, xscrollcommand=message_scrollbar_h.set)
        
        self.message_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        message_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        message_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_status_bar(self):
        """Create the status bar"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_label = ttk.Label(self.status_bar, text="Ready")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # Progress bar (hidden by default)
        self.progress_bar = ttk.Progressbar(self.status_bar, mode='indeterminate')
        
        # File info label
        self.file_info_label = ttk.Label(self.status_bar, text="")
        self.file_info_label.pack(side=tk.RIGHT, padx=5)
    
    def configure_styles(self):
        """Configure custom styles"""
        style = ttk.Style()
        
        # Configure treeview styles
        style.configure("Treeview", font=("Arial", 9))
        style.configure("Treeview.Heading", font=("Arial", 9, "bold"))
    
    def bind_events(self):
        """Bind keyboard and mouse events"""
        # Keyboard shortcuts
        self.root.bind("<Control-o>", lambda e: self.open_file())
        self.root.bind("<Control-f>", lambda e: self.open_search())
        self.root.bind("<Control-q>", lambda e: self.root.quit())
        self.root.bind("<F5>", lambda e: self.refresh_view())
        
        # Search entry events
        self.search_entry.bind("<Return>", lambda e: self.quick_search())
        
        # Contacts tree selection
        self.contacts_tree.bind("<<TreeviewSelect>>", self.on_contact_select)
        
        # Message tree double-click
        self.message_tree.bind("<Double-1>", self.on_message_double_click)
    
    def open_file(self):
        """Open an XML backup file"""
        file_path = filedialog.askopenfilename(
            title="Open SMS XML Backup",
            filetypes=[
                ("XML files", "*.xml"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.load_file(file_path)
    
    def load_file(self, file_path: str):
        """Load and parse an XML backup file"""
        try:
            self.status_label.config(text="Loading file...")
            self.progress_bar.pack(side=tk.RIGHT, padx=5)
            self.progress_bar.start()
            self.root.update()
            
            # Parse the file
            if self.parser.parse_file(file_path):
                self.current_file = file_path
                self.contacts = self.parser.get_contacts()
                
                # Update UI
                self.populate_contacts()
                self.update_file_info()
                
                # Enable menu items
                self.enable_menu_items()
                
                self.status_label.config(text=f"Loaded {len(self.parser.sms_messages)} SMS, "
                                              f"{len(self.parser.mms_messages)} MMS, "
                                              f"{len(self.parser.call_logs)} calls")
            else:
                messagebox.showerror("Error", "Failed to parse XML file. Please check the file format.")
                self.status_label.config(text="Error loading file")
        
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred while loading the file:\n{str(e)}")
            self.status_label.config(text="Error loading file")
        
        finally:
            self.progress_bar.stop()
            self.progress_bar.pack_forget()
    
    def populate_contacts(self):
        """Populate the contacts tree with data"""
        # Clear existing items
        for item in self.contacts_tree.get_children():
            self.contacts_tree.delete(item)
        
        # Get unique phone numbers and their message counts
        phone_counts = {}
        
        # Count SMS messages
        for sms in self.parser.sms_messages:
            phone_counts[sms.address] = phone_counts.get(sms.address, 0) + 1
        
        # Count MMS messages
        for mms in self.parser.mms_messages:
            if mms.address:
                phone_counts[mms.address] = phone_counts.get(mms.address, 0) + 1
        
        # Sort by message count (descending)
        sorted_phones = sorted(phone_counts.items(), key=lambda x: x[1], reverse=True)
        
        # Add to tree
        for phone, count in sorted_phones:
            contact_name = self.contacts.get(phone, "Unknown Contact")
            display_name = f"{contact_name}" if contact_name != "Unknown Contact" else phone
            
            self.contacts_tree.insert("", "end", text=display_name, 
                                    values=(phone, count))
    
    def on_contact_select(self, event):
        """Handle contact selection"""
        selection = self.contacts_tree.selection()
        if selection:
            item = self.contacts_tree.item(selection[0])
            phone_number = item['values'][0]
            contact_name = item['text']
            
            # Update header
            self.message_header.config(text=f"Messages with {contact_name}")
            
            # Load messages for this contact
            self.load_contact_messages(phone_number)
    
    def load_contact_messages(self, phone_number: str):
        """Load messages for a specific contact"""
        messages = self.parser.get_messages_by_contact(phone_number)
        
        # Update conversation view
        self.update_conversation_view(messages)
        
        # Update message list view
        self.update_message_list_view(messages)
    
    def update_conversation_view(self, messages: List[Union[SMSMessage, MMSMessage]]):
        """Update the conversation view with messages"""
        self.conversation_text.config(state=tk.NORMAL)
        self.conversation_text.delete(1.0, tk.END)
        
        for message in messages:
            # Determine message direction and content
            if isinstance(message, SMSMessage):
                is_sent = message.type == 2
                content = message.body
                msg_type = "SMS"
            else:  # MMSMessage
                is_sent = message.msg_box == 2
                # Extract text from MMS parts
                content = ""
                for part in message.parts:
                    if part.text:
                        content += part.text + "\n"
                if not content.strip():
                    content = "[MMS - Media content]"
                msg_type = "MMS"
            
            # Format timestamp
            timestamp = message.get_readable_date()
            
            # Insert message
            tag = "sent" if is_sent else "received"
            if isinstance(message, MMSMessage):
                tag += " mms"
            
            direction = "You" if is_sent else self.contacts.get(message.address, message.address)
            
            self.conversation_text.insert(tk.END, f"{direction} - {timestamp}\n", "timestamp")
            self.conversation_text.insert(tk.END, f"{content}\n\n", tag)
        
        self.conversation_text.config(state=tk.DISABLED)
        # Scroll to bottom
        self.conversation_text.see(tk.END)
    
    def update_message_list_view(self, messages: List[Union[SMSMessage, MMSMessage]]):
        """Update the message list view with messages"""
        # Clear existing items
        for item in self.message_tree.get_children():
            self.message_tree.delete(item)
        
        for message in messages:
            if isinstance(message, SMSMessage):
                direction = "Sent" if message.type == 2 else "Received"
                msg_type = "SMS"
                content = message.body[:100] + "..." if len(message.body) > 100 else message.body
            else:  # MMSMessage
                direction = "Sent" if message.msg_box == 2 else "Received"
                msg_type = "MMS"
                # Extract text from MMS parts
                text_content = ""
                for part in message.parts:
                    if part.text:
                        text_content += part.text + " "
                content = text_content[:100] + "..." if len(text_content) > 100 else text_content
                if not content.strip():
                    content = "[Media content]"
            
            timestamp = message.get_readable_date()
            
            self.message_tree.insert("", "end", text=direction,
                                   values=(msg_type, timestamp, content))
    
    def update_file_info(self):
        """Update file information in status bar"""
        if self.current_file:
            filename = os.path.basename(self.current_file)
            self.file_info_label.config(text=f"File: {filename}")
    
    def enable_menu_items(self):
        """Enable menu items that require a loaded file"""
        # This would enable export/print menu items
        pass
    
    # Placeholder methods for menu actions
    def export_conversation(self):
        messagebox.showinfo("Export", "Export functionality will be implemented")
    
    def print_conversation(self):
        messagebox.showinfo("Print", "Print functionality will be implemented")
    
    def refresh_view(self):
        if self.current_file:
            self.load_file(self.current_file)
    
    def filter_messages(self, filter_type: str):
        self.filter_var.set(filter_type)
        self.on_filter_change()
    
    def on_filter_change(self, event=None):
        # Implement filtering logic
        pass
    
    def open_search(self):
        messagebox.showinfo("Search", "Advanced search functionality will be implemented")
    
    def quick_search(self):
        query = self.search_var.get().strip()
        if query:
            messagebox.showinfo("Search", f"Searching for: {query}")
    
    def show_statistics(self):
        if not self.current_file:
            messagebox.showwarning("No File", "Please load an XML backup file first.")
            return
        
        stats = f"SMS Messages: {len(self.parser.sms_messages)}\n"
        stats += f"MMS Messages: {len(self.parser.mms_messages)}\n"
        stats += f"Call Logs: {len(self.parser.call_logs)}\n"
        stats += f"Unique Contacts: {len(self.contacts)}"
        
        messagebox.showinfo("Statistics", stats)
    
    def show_about(self):
        about_text = """SMS Organizer v1.0

A desktop application for viewing and organizing 
XML SMS backups from Android phones.

Features:
• View SMS and MMS messages
• Organize by contact
• Search functionality
• Export conversations
• Print support

Developed with Python and tkinter"""
        
        messagebox.showinfo("About SMS Organizer", about_text)
    
    def on_message_double_click(self, event):
        """Handle double-click on message in list view"""
        selection = self.message_tree.selection()
        if selection:
            # Switch to conversation view
            self.messages_notebook.select(0)
    
    def run(self):
        """Start the application"""
        self.root.mainloop()


def main():
    """Main entry point"""
    app = SMSOrganizerApp()
    app.run()


if __name__ == "__main__":
    main()
