"""
SMS Organizer - Main GUI Application

A desktop application for viewing and organizing XML SMS backups from Android phones.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
from datetime import datetime
from typing import Dict, List, Optional, Union
from sms_parser import SMSXMLParser, SMSMessage, MMSMessage, CallLog
from theme_manager import ThemeManager, create_theme_menu
from media_viewer import <PERSON>Viewer
from export_manager import ExportManager


class SMSOrganizerApp:
    """Main application class for SMS Organizer"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("SMS Organizer - Phone SMS Backup Viewer")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)

        # Initialize theme manager
        self.theme_manager = ThemeManager()

        # Initialize media viewer
        self.media_viewer = MediaViewer(self.root, self.theme_manager)

        # Initialize parser
        self.parser = SMSXMLParser()
        self.current_file = None
        self.contacts = {}

        # Initialize export manager (will be updated when file is loaded)
        self.export_manager = None

        # Create GUI components
        self.create_menu()
        self.create_toolbar()
        self.create_main_layout()
        self.create_status_bar()

        # Configure styles and apply theme
        self.configure_styles()
        self.apply_theme()

        # Bind events
        self.bind_events()
    
    def create_menu(self):
        """Create the application menu bar"""
        self.menubar = tk.Menu(self.root)
        self.root.config(menu=self.menubar)
        
        # File menu
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open XML Backup...", command=self.open_file, accelerator="Ctrl+O")
        file_menu.add_separator()

        # Export submenu
        export_menu = tk.Menu(file_menu, tearoff=0)
        file_menu.add_cascade(label="Export", menu=export_menu, state="disabled")
        export_menu.add_command(label="Current Conversation as PDF...", command=lambda: self.export_current_conversation("pdf"))
        export_menu.add_command(label="Current Conversation as HTML...", command=lambda: self.export_current_conversation("html"))
        export_menu.add_command(label="Current Conversation as CSV...", command=lambda: self.export_current_conversation("csv"))
        export_menu.add_command(label="Current Conversation as TXT...", command=lambda: self.export_current_conversation("txt"))
        export_menu.add_separator()
        export_menu.add_command(label="All Conversations as CSV...", command=lambda: self.export_all_conversations("csv"))
        export_menu.add_command(label="All Conversations as HTML...", command=lambda: self.export_all_conversations("html"))
        export_menu.add_command(label="All Conversations as TXT...", command=lambda: self.export_all_conversations("txt"))

        # Store reference to export menu for enabling/disabling
        self.export_menu = export_menu
        self.file_export_cascade = file_menu

        file_menu.add_command(label="Generate Report...", command=self.generate_report, state="disabled")
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit, accelerator="Ctrl+Q")
        
        # View menu
        view_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Refresh", command=self.refresh_view, accelerator="F5")
        view_menu.add_separator()
        view_menu.add_command(label="Show SMS Only", command=lambda: self.filter_messages("sms"))
        view_menu.add_command(label="Show MMS Only", command=lambda: self.filter_messages("mms"))
        view_menu.add_command(label="Show All Messages", command=lambda: self.filter_messages("all"))
        view_menu.add_separator()

        # Theme submenu
        theme_menu = create_theme_menu(view_menu, self.theme_manager, self.apply_theme)
        view_menu.add_cascade(label="Themes", menu=theme_menu)
        
        # Tools menu
        tools_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Search Messages...", command=self.open_search, accelerator="Ctrl+F")
        tools_menu.add_command(label="Statistics", command=self.show_statistics)
        
        # Help menu
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def create_toolbar(self):
        """Create the toolbar"""
        self.toolbar = ttk.Frame(self.root)
        self.toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        # Open file button
        ttk.Button(self.toolbar, text="Open XML", command=self.open_file).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Search entry
        ttk.Label(self.toolbar, text="Search:").pack(side=tk.LEFT, padx=2)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(self.toolbar, textvariable=self.search_var, width=20)
        self.search_entry.pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="Search", command=self.quick_search).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Filter options
        ttk.Label(self.toolbar, text="Filter:").pack(side=tk.LEFT, padx=2)
        self.filter_var = tk.StringVar(value="all")
        filter_combo = ttk.Combobox(self.toolbar, textvariable=self.filter_var, 
                                   values=["all", "sms", "mms"], width=10, state="readonly")
        filter_combo.pack(side=tk.LEFT, padx=2)
        filter_combo.bind("<<ComboboxSelected>>", self.on_filter_change)
    
    def create_main_layout(self):
        """Create the main application layout"""
        # Create main paned window
        self.main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Contacts list
        self.left_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.left_frame, weight=1)
        
        # Contacts list
        ttk.Label(self.left_frame, text="Contacts", font=("Arial", 12, "bold")).pack(pady=5)
        
        contacts_frame = ttk.Frame(self.left_frame)
        contacts_frame.pack(fill=tk.BOTH, expand=True)
        
        # Contacts treeview
        self.contacts_tree = ttk.Treeview(contacts_frame, columns=("phone", "count"), show="tree headings")
        self.contacts_tree.heading("#0", text="Contact")
        self.contacts_tree.heading("phone", text="Phone Number")
        self.contacts_tree.heading("count", text="Messages")
        
        self.contacts_tree.column("#0", width=150)
        self.contacts_tree.column("phone", width=120)
        self.contacts_tree.column("count", width=80)
        
        # Scrollbars for contacts tree
        contacts_scrollbar_v = ttk.Scrollbar(contacts_frame, orient=tk.VERTICAL, command=self.contacts_tree.yview)
        contacts_scrollbar_h = ttk.Scrollbar(contacts_frame, orient=tk.HORIZONTAL, command=self.contacts_tree.xview)
        self.contacts_tree.configure(yscrollcommand=contacts_scrollbar_v.set, xscrollcommand=contacts_scrollbar_h.set)
        
        self.contacts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        contacts_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        contacts_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Right panel - Message view
        self.right_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.right_frame, weight=2)
        
        # Message view header
        self.message_header = ttk.Label(self.right_frame, text="Select a contact to view messages", 
                                       font=("Arial", 12, "bold"))
        self.message_header.pack(pady=5)
        
        # Messages notebook for different views
        self.messages_notebook = ttk.Notebook(self.right_frame)
        self.messages_notebook.pack(fill=tk.BOTH, expand=True)
        
        # Conversation view tab
        self.conversation_frame = ttk.Frame(self.messages_notebook)
        self.messages_notebook.add(self.conversation_frame, text="Conversation")
        
        # Create conversation view
        self.create_conversation_view()
        
        # Message list view tab
        self.list_frame = ttk.Frame(self.messages_notebook)
        self.messages_notebook.add(self.list_frame, text="Message List")
        
        # Create message list view
        self.create_message_list_view()
    
    def create_conversation_view(self):
        """Create the conversation-style message view"""
        # Conversation display
        self.conversation_text = scrolledtext.ScrolledText(
            self.conversation_frame, 
            wrap=tk.WORD, 
            state=tk.DISABLED,
            font=("Arial", 10)
        )
        self.conversation_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Configure text tags for different message types (will be updated by theme)
        self.configure_conversation_tags()
    
    def create_message_list_view(self):
        """Create the message list view"""
        # Message list treeview
        self.message_tree = ttk.Treeview(self.list_frame, 
                                        columns=("type", "date", "content"), 
                                        show="tree headings")
        self.message_tree.heading("#0", text="Direction")
        self.message_tree.heading("type", text="Type")
        self.message_tree.heading("date", text="Date/Time")
        self.message_tree.heading("content", text="Content")
        
        self.message_tree.column("#0", width=80)
        self.message_tree.column("type", width=60)
        self.message_tree.column("date", width=150)
        self.message_tree.column("content", width=300)
        
        # Scrollbars for message tree
        message_scrollbar_v = ttk.Scrollbar(self.list_frame, orient=tk.VERTICAL, command=self.message_tree.yview)
        message_scrollbar_h = ttk.Scrollbar(self.list_frame, orient=tk.HORIZONTAL, command=self.message_tree.xview)
        self.message_tree.configure(yscrollcommand=message_scrollbar_v.set, xscrollcommand=message_scrollbar_h.set)
        
        self.message_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        message_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        message_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_status_bar(self):
        """Create the status bar"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_label = ttk.Label(self.status_bar, text="Ready")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # Progress bar (hidden by default)
        self.progress_bar = ttk.Progressbar(self.status_bar, mode='indeterminate')
        
        # File info label
        self.file_info_label = ttk.Label(self.status_bar, text="")
        self.file_info_label.pack(side=tk.RIGHT, padx=5)
    
    def configure_styles(self):
        """Configure custom styles"""
        style = ttk.Style()
        
        # Configure treeview styles
        style.configure("Treeview", font=("Arial", 9))
        style.configure("Treeview.Heading", font=("Arial", 9, "bold"))
    
    def bind_events(self):
        """Bind keyboard and mouse events"""
        # Keyboard shortcuts
        self.root.bind("<Control-o>", lambda e: self.open_file())
        self.root.bind("<Control-f>", lambda e: self.open_search())
        self.root.bind("<Control-q>", lambda e: self.root.quit())
        self.root.bind("<F5>", lambda e: self.refresh_view())
        
        # Search entry events
        self.search_entry.bind("<Return>", lambda e: self.quick_search())
        
        # Contacts tree selection
        self.contacts_tree.bind("<<TreeviewSelect>>", self.on_contact_select)

        # Message tree double-click
        self.message_tree.bind("<Double-1>", self.on_message_double_click)

        # Context menus
        self.create_context_menus()
    
    def open_file(self):
        """Open an XML backup file"""
        file_path = filedialog.askopenfilename(
            title="Open SMS XML Backup",
            filetypes=[
                ("XML files", "*.xml"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.load_file(file_path)
    
    def load_file(self, file_path: str):
        """Load and parse an XML backup file"""
        try:
            self.status_label.config(text="Loading file...")
            self.progress_bar.pack(side=tk.RIGHT, padx=5)
            self.progress_bar.start()
            self.root.update()
            
            # Parse the file
            if self.parser.parse_file(file_path):
                self.current_file = file_path
                self.contacts = self.parser.get_contacts()
                
                # Initialize export manager
                self.export_manager = ExportManager(self.parser, self.contacts)

                # Update UI
                self.populate_contacts()
                self.update_file_info()

                # Enable menu items
                self.enable_menu_items()
                
                self.status_label.config(text=f"Loaded {len(self.parser.sms_messages)} SMS, "
                                              f"{len(self.parser.mms_messages)} MMS, "
                                              f"{len(self.parser.call_logs)} calls")
            else:
                messagebox.showerror("Error", "Failed to parse XML file. Please check the file format.")
                self.status_label.config(text="Error loading file")
        
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred while loading the file:\n{str(e)}")
            self.status_label.config(text="Error loading file")
        
        finally:
            self.progress_bar.stop()
            self.progress_bar.pack_forget()
    
    def populate_contacts(self):
        """Populate the contacts tree with data"""
        # Clear existing items
        for item in self.contacts_tree.get_children():
            self.contacts_tree.delete(item)
        
        # Get unique phone numbers and their message counts
        phone_counts = {}
        
        # Count SMS messages
        for sms in self.parser.sms_messages:
            phone_counts[sms.address] = phone_counts.get(sms.address, 0) + 1
        
        # Count MMS messages
        for mms in self.parser.mms_messages:
            if mms.address:
                phone_counts[mms.address] = phone_counts.get(mms.address, 0) + 1
        
        # Sort by message count (descending)
        sorted_phones = sorted(phone_counts.items(), key=lambda x: x[1], reverse=True)
        
        # Add to tree
        for phone, count in sorted_phones:
            contact_name = self.contacts.get(phone, "Unknown Contact")
            display_name = f"{contact_name}" if contact_name != "Unknown Contact" else phone
            
            self.contacts_tree.insert("", "end", text=display_name, 
                                    values=(phone, count))
    
    def on_contact_select(self, event):
        """Handle contact selection"""
        selection = self.contacts_tree.selection()
        if selection:
            item = self.contacts_tree.item(selection[0])
            phone_number = item['values'][0]
            contact_name = item['text']
            
            # Update header
            self.message_header.config(text=f"Messages with {contact_name}")
            
            # Load messages for this contact
            self.load_contact_messages(phone_number)
    
    def load_contact_messages(self, phone_number: str):
        """Load messages for a specific contact"""
        messages = self.parser.get_messages_by_contact(phone_number)
        
        # Update conversation view
        self.update_conversation_view(messages)
        
        # Update message list view
        self.update_message_list_view(messages)
    
    def update_conversation_view(self, messages: List[Union[SMSMessage, MMSMessage]]):
        """Update the conversation view with messages"""
        # Clear the conversation frame and recreate it with scrollable content
        for widget in self.conversation_frame.winfo_children():
            widget.destroy()

        # Create scrollable frame for conversation
        canvas = tk.Canvas(self.conversation_frame)
        scrollbar = ttk.Scrollbar(self.conversation_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Apply theme to canvas
        if self.theme_manager:
            theme = self.theme_manager.get_current_theme()
            canvas.configure(bg=theme["text_bg"])

        for message in messages:
            # Create message frame
            msg_frame = ttk.Frame(scrollable_frame)
            msg_frame.pack(fill=tk.X, padx=10, pady=5)

            # Determine message direction and content
            if isinstance(message, SMSMessage):
                is_sent = message.type == 2
                content = message.body
                msg_type = "SMS"

                # Create text message widget
                self.create_text_message_widget(msg_frame, message, is_sent, content)

            else:  # MMSMessage
                is_sent = message.msg_box == 2

                # Create MMS message widget with media support
                self.create_mms_message_widget(msg_frame, message, is_sent)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Scroll to bottom
        self.conversation_frame.update_idletasks()
        canvas.yview_moveto(1.0)

    def create_text_message_widget(self, parent_frame, message, is_sent, content):
        """Create a widget for text messages"""
        # Message container
        container = tk.Frame(parent_frame)
        if is_sent:
            container.pack(side=tk.RIGHT, anchor="e", padx=(50, 0))
        else:
            container.pack(side=tk.LEFT, anchor="w", padx=(0, 50))

        # Apply theme colors
        colors = self.theme_manager.get_conversation_colors()
        bg_color = colors["sent_bg"] if is_sent else colors["received_bg"]

        # Timestamp
        timestamp = message.get_readable_date()
        direction = "You" if is_sent else self.contacts.get(message.address, message.address)

        timestamp_label = tk.Label(container, text=f"{direction} - {timestamp}",
                                 font=("Arial", 8), fg=colors["timestamp_fg"])
        if self.theme_manager:
            self.theme_manager.apply_theme_to_widget(timestamp_label, "label")
        timestamp_label.pack(anchor="w" if not is_sent else "e")

        # Message content
        content_label = tk.Label(container, text=content, bg=bg_color,
                               wraplength=300, justify=tk.LEFT, padx=10, pady=5)
        content_label.pack()

    def create_mms_message_widget(self, parent_frame, message, is_sent):
        """Create a widget for MMS messages with media support"""
        # Message container
        container = tk.Frame(parent_frame)
        if is_sent:
            container.pack(side=tk.RIGHT, anchor="e", padx=(50, 0))
        else:
            container.pack(side=tk.LEFT, anchor="w", padx=(0, 50))

        # Apply theme colors
        colors = self.theme_manager.get_conversation_colors()
        bg_color = colors["mms_bg"]

        # Timestamp
        timestamp = message.get_readable_date()
        direction = "You" if is_sent else self.contacts.get(message.address, message.address)

        timestamp_label = tk.Label(container, text=f"{direction} - {timestamp}",
                                 font=("Arial", 8), fg=colors["timestamp_fg"])
        if self.theme_manager:
            self.theme_manager.apply_theme_to_widget(timestamp_label, "label")
        timestamp_label.pack(anchor="w" if not is_sent else "e")

        # MMS content frame
        mms_frame = tk.Frame(container, bg=bg_color, padx=10, pady=5)
        mms_frame.pack()

        # Subject if available
        if message.sub:
            subject_label = tk.Label(mms_frame, text=f"Subject: {message.sub}",
                                   font=("Arial", 9, "bold"), bg=bg_color)
            subject_label.pack(anchor="w")

        # Process MMS parts
        text_content = ""
        media_parts = []

        for part in message.parts:
            if part.text:
                text_content += part.text + "\n"
            elif part.data:
                media_parts.append(part)

        # Display text content
        if text_content.strip():
            text_label = tk.Label(mms_frame, text=text_content.strip(),
                                bg=bg_color, wraplength=300, justify=tk.LEFT)
            text_label.pack(anchor="w", pady=(0, 5))

        # Display media parts
        if media_parts:
            media_container = tk.Frame(mms_frame, bg=bg_color)
            media_container.pack(fill=tk.X)

            for part in media_parts:
                media_widget = self.media_viewer.create_media_widget(media_container, part)
                media_widget.pack(side=tk.LEFT, padx=5, pady=2)

        # If no content at all
        if not text_content.strip() and not media_parts:
            no_content_label = tk.Label(mms_frame, text="[Empty MMS]",
                                      bg=bg_color, fg="gray")
            no_content_label.pack()
    
    def update_message_list_view(self, messages: List[Union[SMSMessage, MMSMessage]]):
        """Update the message list view with messages"""
        # Clear existing items
        for item in self.message_tree.get_children():
            self.message_tree.delete(item)
        
        for message in messages:
            if isinstance(message, SMSMessage):
                direction = "Sent" if message.type == 2 else "Received"
                msg_type = "SMS"
                content = message.body[:100] + "..." if len(message.body) > 100 else message.body
            else:  # MMSMessage
                direction = "Sent" if message.msg_box == 2 else "Received"
                msg_type = "MMS"
                # Extract text from MMS parts
                text_content = ""
                for part in message.parts:
                    if part.text:
                        text_content += part.text + " "
                content = text_content[:100] + "..." if len(text_content) > 100 else text_content
                if not content.strip():
                    content = "[Media content]"
            
            timestamp = message.get_readable_date()
            
            self.message_tree.insert("", "end", text=direction,
                                   values=(msg_type, timestamp, content))
    
    def update_file_info(self):
        """Update file information in status bar"""
        if self.current_file:
            filename = os.path.basename(self.current_file)
            self.file_info_label.config(text=f"File: {filename}")
    
    def enable_menu_items(self):
        """Enable menu items that require a loaded file"""
        # Enable export menu
        self.file_export_cascade.entryconfig("Export", state="normal")

        # Enable generate report
        for i in range(self.file_export_cascade.index("end") + 1):
            try:
                if self.file_export_cascade.entrycget(i, "label") == "Generate Report...":
                    self.file_export_cascade.entryconfig(i, state="normal")
                    break
            except:
                pass
    
    # Placeholder methods for menu actions
    def export_conversation(self):
        messagebox.showinfo("Export", "Export functionality will be implemented")
    
    def print_conversation(self):
        messagebox.showinfo("Print", "Print functionality will be implemented")
    
    def refresh_view(self):
        if self.current_file:
            self.load_file(self.current_file)
    
    def filter_messages(self, filter_type: str):
        self.filter_var.set(filter_type)
        self.on_filter_change()
    
    def on_filter_change(self, event=None):
        # Implement filtering logic
        pass
    
    def open_search(self):
        messagebox.showinfo("Search", "Advanced search functionality will be implemented")
    
    def quick_search(self):
        query = self.search_var.get().strip()
        if query:
            messagebox.showinfo("Search", f"Searching for: {query}")
    
    def show_statistics(self):
        if not self.current_file:
            messagebox.showwarning("No File", "Please load an XML backup file first.")
            return
        
        stats = f"SMS Messages: {len(self.parser.sms_messages)}\n"
        stats += f"MMS Messages: {len(self.parser.mms_messages)}\n"
        stats += f"Call Logs: {len(self.parser.call_logs)}\n"
        stats += f"Unique Contacts: {len(self.contacts)}"
        
        messagebox.showinfo("Statistics", stats)
    
    def show_about(self):
        about_text = """SMS Organizer v1.0

A desktop application for viewing and organizing 
XML SMS backups from Android phones.

Features:
• View SMS and MMS messages
• Organize by contact
• Search functionality
• Export conversations
• Print support

Developed with Python and tkinter"""
        
        messagebox.showinfo("About SMS Organizer", about_text)
    
    def on_message_double_click(self, event):
        """Handle double-click on message in list view"""
        selection = self.message_tree.selection()
        if selection:
            # Switch to conversation view
            self.messages_notebook.select(0)

    def configure_conversation_tags(self):
        """Configure conversation text tags with current theme colors"""
        colors = self.theme_manager.get_conversation_colors()

        self.conversation_text.tag_configure("sent",
                                           background=colors["sent_bg"],
                                           justify="right",
                                           rmargin=50, lmargin2=50)
        self.conversation_text.tag_configure("received",
                                           background=colors["received_bg"],
                                           justify="left",
                                           lmargin1=50, lmargin2=50)
        self.conversation_text.tag_configure("timestamp",
                                           foreground=colors["timestamp_fg"],
                                           font=("Arial", 8))
        self.conversation_text.tag_configure("mms",
                                           background=colors["mms_bg"])

    def apply_theme(self):
        """Apply the current theme to all widgets"""
        # Configure ttk styles
        style = ttk.Style()
        self.theme_manager.configure_ttk_style(style)

        # Apply theme to root window
        theme = self.theme_manager.get_current_theme()
        self.root.configure(bg=theme["bg"])

        # Apply theme to menu
        self.theme_manager.apply_theme_to_widget(self.menubar, "menu")

        # Apply theme to conversation text
        self.theme_manager.apply_theme_to_widget(self.conversation_text, "text")
        self.configure_conversation_tags()

        # Apply theme to search entry
        self.theme_manager.apply_theme_to_widget(self.search_entry, "entry")

        # Update all frames and labels
        self.update_widget_themes()

    def update_widget_themes(self):
        """Update themes for all widgets recursively"""
        def update_widget(widget):
            widget_class = widget.winfo_class()

            if widget_class == "Frame":
                self.theme_manager.apply_theme_to_widget(widget, "frame")
            elif widget_class == "Label":
                self.theme_manager.apply_theme_to_widget(widget, "label")
            elif widget_class == "Text":
                self.theme_manager.apply_theme_to_widget(widget, "text")
            elif widget_class == "Entry":
                self.theme_manager.apply_theme_to_widget(widget, "entry")
            elif widget_class == "Button":
                self.theme_manager.apply_theme_to_widget(widget, "button")

            # Recursively update children
            for child in widget.winfo_children():
                if child.winfo_class() not in ["Treeview", "Notebook", "Combobox"]:  # Skip ttk widgets
                    update_widget(child)

        # Start from root
        update_widget(self.root)

    def create_context_menus(self):
        """Create right-click context menus"""
        # Contacts tree context menu
        self.contacts_context_menu = tk.Menu(self.root, tearoff=0)
        self.contacts_context_menu.add_command(label="Export as PDF...", command=lambda: self.export_selected_conversation("pdf"))
        self.contacts_context_menu.add_command(label="Export as HTML...", command=lambda: self.export_selected_conversation("html"))
        self.contacts_context_menu.add_command(label="Export as CSV...", command=lambda: self.export_selected_conversation("csv"))
        self.contacts_context_menu.add_command(label="Export as TXT...", command=lambda: self.export_selected_conversation("txt"))
        self.contacts_context_menu.add_separator()
        self.contacts_context_menu.add_command(label="View Statistics", command=self.show_contact_statistics)

        # Bind context menu to contacts tree
        self.contacts_tree.bind("<Button-3>", self.show_contacts_context_menu)
        if os.name == 'posix':  # macOS
            self.contacts_tree.bind("<Button-2>", self.show_contacts_context_menu)

    def show_contacts_context_menu(self, event):
        """Show context menu for contacts tree"""
        # Select the item under cursor
        item = self.contacts_tree.identify_row(event.y)
        if item:
            self.contacts_tree.selection_set(item)
            try:
                self.contacts_context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                self.contacts_context_menu.grab_release()

    def export_selected_conversation(self, format_type: str):
        """Export the currently selected conversation"""
        selection = self.contacts_tree.selection()
        if not selection or not self.export_manager:
            return

        item = self.contacts_tree.item(selection[0])
        phone_number = item['values'][0]
        contact_name = item['text']

        self.export_manager.export_conversation(phone_number, contact_name, format_type)

    def export_current_conversation(self, format_type: str):
        """Export the currently viewed conversation"""
        selection = self.contacts_tree.selection()
        if not selection or not self.export_manager:
            messagebox.showwarning("No Selection", "Please select a contact first.")
            return

        item = self.contacts_tree.item(selection[0])
        phone_number = item['values'][0]
        contact_name = item['text']

        self.export_manager.export_conversation(phone_number, contact_name, format_type)

    def export_all_conversations(self, format_type: str):
        """Export all conversations"""
        if not self.export_manager:
            messagebox.showwarning("No Data", "Please load an XML backup file first.")
            return

        self.export_manager.export_all_conversations(format_type)

    def generate_report(self):
        """Generate a comprehensive report with statistics"""
        if not self.export_manager:
            messagebox.showwarning("No Data", "Please load an XML backup file first.")
            return

        stats = self.export_manager.get_statistics()

        # Create report content
        report = f"""SMS Backup Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

SUMMARY STATISTICS
==================
Total SMS Messages: {stats['total_sms']}
Total MMS Messages: {stats['total_mms']}
Total Contacts: {stats['total_contacts']}

MESSAGE BREAKDOWN
=================
Sent SMS: {stats['sent_sms']}
Received SMS: {stats['received_sms']}
Sent MMS: {stats['sent_mms']}
Received MMS: {stats['received_mms']}

ACTIVITY ANALYSIS
=================
Most Active Contact: {stats['most_active_contact']}
Messages with Most Active: {stats['most_active_count']}

CONTACT LIST
============
"""

        # Add contact list with message counts
        contact_counts = {}
        for phone, name in self.contacts.items():
            messages = self.parser.get_messages_by_contact(phone)
            contact_counts[name] = len(messages)

        # Sort by message count
        sorted_contacts = sorted(contact_counts.items(), key=lambda x: x[1], reverse=True)

        for name, count in sorted_contacts:
            report += f"{name}: {count} messages\n"

        # Save report
        filename = filedialog.asksaveasfilename(
            title="Save Report",
            defaultextension=".txt",
            initialvalue="sms_backup_report.txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(report)
                messagebox.showinfo("Report Generated", f"Report saved to {filename}")

                if messagebox.askyesno("Open Report", "Would you like to open the report?"):
                    self.export_manager.open_file(filename)
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save report:\n{str(e)}")

    def show_contact_statistics(self):
        """Show statistics for selected contact"""
        selection = self.contacts_tree.selection()
        if not selection:
            return

        item = self.contacts_tree.item(selection[0])
        phone_number = item['values'][0]
        contact_name = item['text']

        messages = self.parser.get_messages_by_contact(phone_number)

        sms_count = len([msg for msg in messages if hasattr(msg, 'type')])
        mms_count = len([msg for msg in messages if not hasattr(msg, 'type')])
        sent_count = len([msg for msg in messages if (hasattr(msg, 'type') and msg.type == 2) or (not hasattr(msg, 'type') and msg.msg_box == 2)])
        received_count = len(messages) - sent_count

        # Find date range
        if messages:
            dates = [msg.date for msg in messages]
            first_date = datetime.fromtimestamp(min(dates) / 1000).strftime('%Y-%m-%d')
            last_date = datetime.fromtimestamp(max(dates) / 1000).strftime('%Y-%m-%d')
        else:
            first_date = "N/A"
            last_date = "N/A"

        stats_text = f"""Contact Statistics: {contact_name}
Phone: {phone_number}

Message Counts:
• Total Messages: {len(messages)}
• SMS Messages: {sms_count}
• MMS Messages: {mms_count}
• Sent: {sent_count}
• Received: {received_count}

Date Range:
• First Message: {first_date}
• Last Message: {last_date}"""

        messagebox.showinfo("Contact Statistics", stats_text)

    def run(self):
        """Start the application"""
        self.root.mainloop()


def main():
    """Main entry point"""
    app = SMSOrganizerApp()
    app.run()


if __name__ == "__main__":
    main()
