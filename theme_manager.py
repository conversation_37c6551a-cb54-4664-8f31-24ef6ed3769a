"""
Theme Manager for SMS Organizer

Handles dark mode, light mode, and custom color themes.
"""

import tkinter as tk
from tkinter import ttk
import json
import os
from typing import Dict, Any


class ThemeManager:
    """Manages application themes and color schemes"""
    
    def __init__(self):
        self.current_theme = "light"
        self.themes = {
            "light": {
                "bg": "#FFFFFF",
                "fg": "#000000",
                "select_bg": "#0078D4",
                "select_fg": "#FFFFFF",
                "entry_bg": "#FFFFFF",
                "entry_fg": "#000000",
                "button_bg": "#F0F0F0",
                "button_fg": "#000000",
                "frame_bg": "#F5F5F5",
                "text_bg": "#FFFFFF",
                "text_fg": "#000000",
                "sent_bg": "#DCF8C6",
                "received_bg": "#FFFFFF",
                "timestamp_fg": "#666666",
                "mms_bg": "#E1F5FE",
                "menu_bg": "#FFFFFF",
                "menu_fg": "#000000",
                "status_bg": "#F0F0F0",
                "status_fg": "#000000",
                "tree_bg": "#FFFFFF",
                "tree_fg": "#000000",
                "tree_select_bg": "#0078D4",
                "tree_select_fg": "#FFFFFF"
            },
            "dark": {
                "bg": "#2B2B2B",
                "fg": "#FFFFFF",
                "select_bg": "#404040",
                "select_fg": "#FFFFFF",
                "entry_bg": "#404040",
                "entry_fg": "#FFFFFF",
                "button_bg": "#404040",
                "button_fg": "#FFFFFF",
                "frame_bg": "#353535",
                "text_bg": "#2B2B2B",
                "text_fg": "#FFFFFF",
                "sent_bg": "#2D5A2D",
                "received_bg": "#404040",
                "timestamp_fg": "#AAAAAA",
                "mms_bg": "#1E3A5F",
                "menu_bg": "#2B2B2B",
                "menu_fg": "#FFFFFF",
                "status_bg": "#353535",
                "status_fg": "#FFFFFF",
                "tree_bg": "#2B2B2B",
                "tree_fg": "#FFFFFF",
                "tree_select_bg": "#404040",
                "tree_select_fg": "#FFFFFF"
            },
            "blue": {
                "bg": "#E3F2FD",
                "fg": "#0D47A1",
                "select_bg": "#1976D2",
                "select_fg": "#FFFFFF",
                "entry_bg": "#FFFFFF",
                "entry_fg": "#0D47A1",
                "button_bg": "#BBDEFB",
                "button_fg": "#0D47A1",
                "frame_bg": "#F3E5F5",
                "text_bg": "#FFFFFF",
                "text_fg": "#0D47A1",
                "sent_bg": "#C8E6C9",
                "received_bg": "#E1F5FE",
                "timestamp_fg": "#424242",
                "mms_bg": "#E8F5E8",
                "menu_bg": "#E3F2FD",
                "menu_fg": "#0D47A1",
                "status_bg": "#BBDEFB",
                "status_fg": "#0D47A1",
                "tree_bg": "#FFFFFF",
                "tree_fg": "#0D47A1",
                "tree_select_bg": "#1976D2",
                "tree_select_fg": "#FFFFFF"
            },
            "green": {
                "bg": "#E8F5E8",
                "fg": "#1B5E20",
                "select_bg": "#4CAF50",
                "select_fg": "#FFFFFF",
                "entry_bg": "#FFFFFF",
                "entry_fg": "#1B5E20",
                "button_bg": "#C8E6C9",
                "button_fg": "#1B5E20",
                "frame_bg": "#F1F8E9",
                "text_bg": "#FFFFFF",
                "text_fg": "#1B5E20",
                "sent_bg": "#A5D6A7",
                "received_bg": "#E8F5E8",
                "timestamp_fg": "#424242",
                "mms_bg": "#C8E6C9",
                "menu_bg": "#E8F5E8",
                "menu_fg": "#1B5E20",
                "status_bg": "#C8E6C9",
                "status_fg": "#1B5E20",
                "tree_bg": "#FFFFFF",
                "tree_fg": "#1B5E20",
                "tree_select_bg": "#4CAF50",
                "tree_select_fg": "#FFFFFF"
            }
        }
        
        self.config_file = "theme_config.json"
        self.load_theme_config()
    
    def load_theme_config(self):
        """Load theme configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    self.current_theme = config.get('current_theme', 'light')
        except Exception as e:
            print(f"Error loading theme config: {e}")
            self.current_theme = 'light'
    
    def save_theme_config(self):
        """Save theme configuration to file"""
        try:
            config = {'current_theme': self.current_theme}
            with open(self.config_file, 'w') as f:
                json.dump(config, f)
        except Exception as e:
            print(f"Error saving theme config: {e}")
    
    def get_current_theme(self) -> Dict[str, str]:
        """Get the current theme colors"""
        return self.themes.get(self.current_theme, self.themes['light'])
    
    def set_theme(self, theme_name: str):
        """Set the current theme"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            self.save_theme_config()
    
    def get_available_themes(self) -> list:
        """Get list of available theme names"""
        return list(self.themes.keys())
    
    def apply_theme_to_widget(self, widget, widget_type: str = "default"):
        """Apply current theme to a specific widget"""
        theme = self.get_current_theme()
        
        try:
            if widget_type == "text":
                widget.configure(
                    bg=theme["text_bg"],
                    fg=theme["text_fg"],
                    insertbackground=theme["text_fg"],
                    selectbackground=theme["select_bg"],
                    selectforeground=theme["select_fg"]
                )
            elif widget_type == "entry":
                widget.configure(
                    bg=theme["entry_bg"],
                    fg=theme["entry_fg"],
                    insertbackground=theme["entry_fg"],
                    selectbackground=theme["select_bg"],
                    selectforeground=theme["select_fg"]
                )
            elif widget_type == "button":
                widget.configure(
                    bg=theme["button_bg"],
                    fg=theme["button_fg"],
                    activebackground=theme["select_bg"],
                    activeforeground=theme["select_fg"]
                )
            elif widget_type == "frame":
                widget.configure(bg=theme["frame_bg"])
            elif widget_type == "label":
                widget.configure(
                    bg=theme["bg"],
                    fg=theme["fg"]
                )
            elif widget_type == "menu":
                widget.configure(
                    bg=theme["menu_bg"],
                    fg=theme["menu_fg"],
                    activebackground=theme["select_bg"],
                    activeforeground=theme["select_fg"]
                )
            else:  # default
                widget.configure(
                    bg=theme["bg"],
                    fg=theme["fg"]
                )
        except tk.TclError:
            # Some widgets don't support all options
            pass
    
    def configure_ttk_style(self, style: ttk.Style):
        """Configure ttk styles for current theme"""
        theme = self.get_current_theme()
        
        # Configure Treeview
        style.configure("Treeview",
                       background=theme["tree_bg"],
                       foreground=theme["tree_fg"],
                       fieldbackground=theme["tree_bg"])
        
        style.map("Treeview",
                 background=[('selected', theme["tree_select_bg"])],
                 foreground=[('selected', theme["tree_select_fg"])])
        
        # Configure Treeview headings
        style.configure("Treeview.Heading",
                       background=theme["button_bg"],
                       foreground=theme["button_fg"])
        
        # Configure Notebook
        style.configure("TNotebook",
                       background=theme["bg"])
        
        style.configure("TNotebook.Tab",
                       background=theme["button_bg"],
                       foreground=theme["button_fg"])
        
        style.map("TNotebook.Tab",
                 background=[('selected', theme["select_bg"])],
                 foreground=[('selected', theme["select_fg"])])
        
        # Configure Frame
        style.configure("TFrame",
                       background=theme["frame_bg"])
        
        # Configure Label
        style.configure("TLabel",
                       background=theme["bg"],
                       foreground=theme["fg"])
        
        # Configure Button
        style.configure("TButton",
                       background=theme["button_bg"],
                       foreground=theme["button_fg"])
        
        style.map("TButton",
                 background=[('active', theme["select_bg"])],
                 foreground=[('active', theme["select_fg"])])
        
        # Configure Entry
        style.configure("TEntry",
                       fieldbackground=theme["entry_bg"],
                       foreground=theme["entry_fg"])
        
        # Configure Combobox
        style.configure("TCombobox",
                       fieldbackground=theme["entry_bg"],
                       foreground=theme["entry_fg"])
        
        # Configure Separator
        style.configure("TSeparator",
                       background=theme["fg"])
        
        # Configure Progressbar
        style.configure("TProgressbar",
                       background=theme["select_bg"])
    
    def get_conversation_colors(self) -> Dict[str, str]:
        """Get colors specifically for conversation view"""
        theme = self.get_current_theme()
        return {
            "sent_bg": theme["sent_bg"],
            "received_bg": theme["received_bg"],
            "timestamp_fg": theme["timestamp_fg"],
            "mms_bg": theme["mms_bg"],
            "text_bg": theme["text_bg"],
            "text_fg": theme["text_fg"]
        }


def create_theme_menu(parent_menu, theme_manager, callback=None):
    """Create a theme submenu"""
    theme_menu = tk.Menu(parent_menu, tearoff=0)
    
    theme_var = tk.StringVar(value=theme_manager.current_theme)
    
    for theme_name in theme_manager.get_available_themes():
        display_name = theme_name.title()
        theme_menu.add_radiobutton(
            label=display_name,
            variable=theme_var,
            value=theme_name,
            command=lambda t=theme_name: change_theme(theme_manager, t, callback)
        )
    
    return theme_menu


def change_theme(theme_manager, theme_name, callback=None):
    """Change the current theme"""
    theme_manager.set_theme(theme_name)
    if callback:
        callback()
